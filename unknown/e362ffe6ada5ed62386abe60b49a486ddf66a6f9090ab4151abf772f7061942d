<?php declare(strict_types = 1);

namespace App\Domain\Api\Response;

use App\Domain\Building\BuildingType;

class BuildingTypeResDto extends ResDto {

	public string $slug;
	public string $name;
	public string $description;
	/** @var ResourceAmountResDto[] */
	public array $cost;
	public array $demolishGain;

	public int $buildTime;
	public int $demolishTime;
	public int $range;

	public int $maxLevel;

	public bool $uniqueInWorld;
	public bool $uniqueInRegion;

	/** @var array<array<string, array<int|string, int|string>>>|null */
	public ?array $productions = NULL;

	public ?array $unitProductions = NULL;

	public static function from(BuildingType $buildingType) : self {
		$self = new self();
		$self->slug = $buildingType->getSlug();
		$self->name = $buildingType->getName();
		$self->description = $buildingType->getDescription();
		$self->cost = array_map(fn($resource) => ResourceAmountResDto::from($resource['type'], $resource['amount']), $buildingType->getBuildCost());;
		$self->demolishGain = array_map(fn($resource) => ['type' => $resource['type']->toArray(), 'amount' => $resource['amount']], $buildingType->getDemolishGain());

		$self->buildTime = $buildingType->getBuildTime();
		$self->demolishTime = $buildingType->getDemolishTime();
		$self->range = $buildingType->getRange();

		$self->maxLevel = BuildingType::getMaxLevel();

		$self->uniqueInWorld = $buildingType->isUniqueInWorld();
		$self->uniqueInRegion = $buildingType->isUniqueInRegion();

		if ($buildingType->canProduce()) {
			$self->productions = array_map(
				fn($production) => [
					'type' => $production['type']->toArray(),
					'levels' => $production['levels']
				],
				$buildingType->getProductions(),
			);
		}

		if ($buildingType->canProduceUnits()) {
			$self->unitProductions = $buildingType->getUnitProductions()->map(fn($production) => UnitProductionResDto::fromBuildingUnitProduction($production))->getValues();
		}

		return $self;
	}

}