includes:
	- vendor/contributte/phpstan/phpstan.neon
	- vendor/phpstan/phpstan-doctrine/extension.neon

parameters:
	level: 9

	tmpDir: %currentWorkingDirectory%/var/temp/phpstan

	ignoreErrors:
		- identifier: ternary.shortNotAllowed

	fileExtensions:
		- php
		- phpt

	paths:
		- app
		- bin

	doctrine:
		ormRepositoryClass: App\Model\Database\Repository\AbstractRepository
		objectManagerLoader: tests/entity-manager.php
