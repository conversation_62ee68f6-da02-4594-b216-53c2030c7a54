<?php declare(strict_types = 1);

namespace App\Domain\Building;

use App\Domain\Resource\ResourceType;
use App\Domain\Unit\UnitDefinition;
use App\Model\Database\Entity\AbstractEntity;
use App\Model\Database\Entity\TId;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: BuildingTypeRepository::class)]
#[ORM\Table(name: 'game_building_type')]
class BuildingType extends AbstractEntity {

	use TId;

	#[ORM\Column(type: 'string', length: 50, unique: true, nullable: false)]
	private string $slug;

	#[ORM\Column(type: 'string', length: 50, nullable: false)]
	private string $name;

	#[ORM\Column(type: 'text', nullable: false)]
	private string $description;

	/** @var int Build time in minutes */
	#[ORM\Column(type: 'integer', nullable: false, options: ['default' => 30])]
	private int $buildTime = 30;

	/** @var int Demolish time in minutes */
	#[ORM\Column(type: 'integer', nullable: false, options: ['default' => 5])]
	private int $demolishTime = 5;

	/** @var array<string, int> */
	#[ORM\Column(type: 'json', nullable: false, options: ['default' => '{}'])]
	private array $buildCost = [];

	/** @var array<string, int> */
	#[ORM\Column(type: 'json', nullable: false, options: ['default' => '{}'])]
	private array $demolishGain = [];

	#[ORM\Column(type: 'integer', nullable: false, options: ['default' => 150])]
	private int $range = 150;

	/**
	 * Production levels (per minute)
	 *
	 * @var array<ResourceType::*, array<int, int>>
	 */
	#[ORM\Column(type: 'json', nullable: false, options: ['default' => '{}'])]
	private array $productions = [];

	#[ORM\Column(type: 'boolean', nullable: false, options: ['default' => false])]
	private bool $uniqueInWorld = false;

	#[ORM\Column(type: 'boolean', nullable: false, options: ['default' => false])]
	private bool $uniqueInRegion = false;

	#[ORM\Column(type: 'integer', nullable: false, options: ['default' => 0])]
	private int $attack = 0;

	#[ORM\Column(type: 'integer', nullable: false, options: ['default' => 0])]
	private int $defense = 0;

	/**
	 * @var Collection<int, BuildingTypeUnitProduction>
	 */
   #[ORM\OneToMany(mappedBy: 'buildingType', targetEntity: BuildingTypeUnitProduction::class, cascade: ['persist'], orphanRemoval: true)]
   private Collection $unitProductions;

	public function __construct(
		string $slug,
		string $name,
		string $description,
	) {
		$this->slug = $slug;
		$this->name = $name;
		$this->description = $description;
		$this->unitProductions = new ArrayCollection();
	}

	public function getSlug() : string {
		return $this->slug;
	}

	public function getName() : string {
		return $this->name;
	}

	public function setName(string $name) : void {
		$this->name = $name;
	}

	public function getDescription() : string {
		return $this->description;
	}

	public function setDescription(string $description) : void {
		$this->description = $description;
	}

	/**
	 * @return int Build time in minutes
	 */
	public function getBuildTime() : int {
		return $this->buildTime;
	}

	public function setBuildTime(int $buildTime) : void {
		$this->buildTime = $buildTime;
	}

	/**
	 * @return int Demolish time in minutes
	 */
	public function getDemolishTime() : int {
		return $this->demolishTime;
	}

	public function setDemolishTime(int $demolishTime) : void {
		$this->demolishTime = $demolishTime;
	}

	/**
	 * @return array{type: ResourceType, amount: int}[]
	 */
	public function getDemolishGain(int $level = 1) : array {
		return array_map(
			fn(string $resource, int $amount) => [
				'type' => ResourceType::from($resource),
				'amount' => $amount * $level,
			],
			array_keys($this->demolishGain),
			array_values($this->demolishGain),
		);
	}

	public function setDemolishGain(ResourceType $type, int $gain) : void {
		$this->demolishGain[$type->value] = $gain;
	}

	public function removeDemolishGain(ResourceType $type) : void {
		unset($this->demolishGain[$type->value]);
	}

	public function clearDemolishGain() : void {
		$this->demolishGain = [];
	}

	/**
	 * @return array{type: ResourceType, amount: int}[]
	 */
	public function getBuildCost(int $level = 1) : array {
		return array_map(
			fn(string $resource, int $amount) => [
				'type' => ResourceType::from($resource),
				'amount' => $amount * $level,
			],
			array_keys($this->buildCost),
			array_values($this->buildCost),
		);
	}

	public function setBuildCost(ResourceType $type, int $buildCost) : void {
		$this->buildCost[$type->value] = $buildCost;
	}

	public function removeBuildCost(ResourceType $type) : void {
		unset($this->buildCost[$type->value]);
	}

	public function clearBuildCost() : void {
		$this->buildCost = [];
	}

	public function getRange() : int {
		return $this->range;
	}

	public function setRange(int $range) : void {
		$this->range = $range;
	}

	public static function getMaxLevel() : int {
		return 5;
	}

	/**
	 * @return array<array{type: ResourceType, levels: array<int, int>}>
	 */
	public function getProductions() : array {
		return array_map(
			fn(string $resource, array $lvls) => [
				'type' => ResourceType::from($resource),
				'levels' => $lvls,
			],
			array_keys($this->productions),
			array_values($this->productions),
		);
	}

	/**
	 * @return array<int, int> Production levels
	 */
	public function getProduction(ResourceType $resource) : array {
		return $this->productions[$resource->value];
	}

	public function getProductionLvl(ResourceType $resource, int $lvl) : int {
		return $this->productions[$resource->value][$lvl];
	}

	/**
	 * @param array<int, int> $levels
	 */
	public function addProduction(ResourceType $resource, array $levels) : void {
		if (isset($this->productions[$resource->value])) {
			return;
		}

		$this->productions[$resource->value] = $levels;
	}

	public function removeProduction(ResourceType $resource) : void {
		unset($this->productions[$resource->value]);
	}

	public function canProduce(ResourceType $resource = NULL) : bool {
		if ($resource === NULL) {
			return $this->productions !== [];
		}

		return isset($this->productions[$resource->value]);
	}

	public function hasProduction() : bool {
		return $this->productions !== [];
	}

	public function clearProductions() : void {
		$this->productions = [];
	}

	public function isUniqueInWorld() : bool {
		return $this->uniqueInWorld;
	}

	public function setUniqueInWorld(bool $uniqueInWorld) : void {
		$this->uniqueInWorld = $uniqueInWorld;
	}

	public function isUniqueInRegion() : bool {
		return $this->uniqueInRegion;
	}

	public function setUniqueInRegion(bool $uniqueInRegion) : void {
		$this->uniqueInRegion = $uniqueInRegion;
	}

	public function getAttack() : int {
		return $this->attack;
	}

	public function setAttack(int $attack) : void {
		$this->attack = $attack;
	}

	public function getDefense() : int {
		return $this->defense;
	}

	public function setDefense(int $defense) : void {
		$this->defense = $defense;
	}

	/**
	 * @return Collection<int, BuildingTypeUnitProduction>
	 */
	public function getUnitProductions() : Collection {
		return $this->unitProductions;
	}

	public function canProduceUnits(UnitDefinition $unitDefinition = NULL) : bool {
		if ($unitDefinition === NULL) {
			return $this->unitProductions->isEmpty() === FALSE;
		}

		return $this->unitProductions
			->exists(fn(int $key, BuildingTypeUnitProduction $unitProduction) => $unitProduction->getUnitDefinition() === $unitDefinition);

	}

	public function getUnitProduction(UnitDefinition $unitDefinition) : ?BuildingTypeUnitProduction {
		return $this->unitProductions
			->filter(fn(BuildingTypeUnitProduction $unitProduction) => $unitProduction->getUnitDefinition() === $unitDefinition)
			->first() ?: NULL;

	}

	public function hasUnitProduction(UnitDefinition $unitDefinition) : bool {
		return $this->getUnitProduction($unitDefinition) !== NULL;
	}

	/**
	 * @param array<ResourceType::*, int> $cost
	 */
	public function addUnitProduction(UnitDefinition $unitDefinition, int $productionTime, array $cost) : void {
		$this->unitProductions->add(new BuildingTypeUnitProduction($this, $unitDefinition, $productionTime, $cost));
	}

	public function removeUnitProduction(UnitDefinition $unitDefinition) : void {
		foreach ($this->unitProductions as $unitProduction) {
			if ($unitProduction->getUnitDefinition() === $unitDefinition) {
				$this->unitProductions->removeElement($unitProduction);
				break;
			}
		}
	}

	public function clearUnitProductions() : void {
		$this->unitProductions->clear();
	}

}