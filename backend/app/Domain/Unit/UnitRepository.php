<?php declare(strict_types = 1);

namespace App\Domain\Unit;

use App\Domain\Building\Building;
use App\Domain\Player\Player;
use App\Model\Database\Repository\AbstractRepository;

/**
 * @method Unit|null find(string $id)
 * @method Unit|null findOneBy(array $criteria)
 * @method Unit[] findAll()
 * @method Unit[] findBy(array $criteria)
 *
 * @extends AbstractRepository<Unit>
 */
class UnitRepository extends AbstractRepository
{
    /**
     * @return Unit[]
     */
    public function findAllByPlayer(Player $player): array
    {
        return $this->createQueryBuilder('u')
            ->join('u.building', 'b')
            ->where('b.player = :player')
            ->setParameter('player', $player)
            ->getQuery()
            ->getResult();
    }

    /**
     * @return Unit[]
     */
    public function findAllByBuilding(Building $building): array
    {
        return $this->createQueryBuilder('u')
            ->where('u.building = :building')
            ->setParameter('building', $building)
            ->getQuery()
            ->getResult();
    }

    /**
     * Find units that need food consumption check
     * 
     * @return Unit[]
     */
    public function findUnitsToCheckFood(int $limit, int $offset, string $period = '1 hour'): array
    {
        return $this->createQueryBuilder('u')
            ->where('u.updatedAt <= :checkTime')
            ->setParameter('checkTime', new \DateTime('-' . $period))
            ->setMaxResults($limit)
            ->setFirstResult($offset)
            ->getQuery()
            ->getResult();
    }

    public function createUnit(Building $building, UnitDefinition $unitDefinition): Unit
    {
        $unit = new Unit($unitDefinition);
        $unit->setBuilding($building);
        $this->getEntityManager()->persist($unit);
        $this->getEntityManager()->flush();
        return $unit;
    }
}