<?php declare(strict_types = 1);

namespace App\Domain\Api\Response;

use Apitte\Core\Mapping\Response\BasicEntity;

abstract class ResDto extends BasicEntity {

	public function toArray() : array {
		$data = [];
		$properties = $this->getProperties();

		foreach ($properties as $property) {
			/** @phpstan-ignore-next-line */
			if (!isset($this->{$property['name']})) {
				continue;
			}

			// Convert camelCase to snake_case
			$camelCase = lcfirst((string) $property['name']);
			$snakeCase = preg_replace('/[A-Z]/', '_\\0', $camelCase);
			$propertyName = strtolower($snakeCase ?? $camelCase);

			/** @phpstan-ignore-next-line */
			$data[$propertyName] = $this->{$property['name']};
		}

		return $data;
	}

}