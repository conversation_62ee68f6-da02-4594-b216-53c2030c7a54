<?php declare(strict_types = 1);

namespace App\Domain\Api\Response;

use App\Domain\Unit\Unit;
use App\Domain\Unit\UnitDefinition;

class UnitResDto
{
    public string $id;
    public string $name;
    public string $description;
    public string $buildingId;
    /** @var array<string, mixed> */
    public array $stats;
    public bool $isWeakened;
    public float $foodConsumptionRate;

    /**
     * @param array<string, mixed> $stats
     */
    public function __construct(
        string $id,
        string $name,
        string $description,
        string $buildingId,
        array $stats,
        bool $isWeakened,
        float $foodConsumptionRate
    ) {
        $this->id = $id;
        $this->name = $name;
        $this->description = $description;
        $this->buildingId = $buildingId;
        $this->stats = $stats;
        $this->isWeakened = $isWeakened;
        $this->foodConsumptionRate = $foodConsumptionRate;
    }

    public static function from(Unit $unit): self
    {
        $definition = $unit->getUnitDefinition();
        
        return new self(
            (string)$unit->getId(),
            $definition->getName(),
            $definition->getDescription(),
            (string)$unit->getBuilding()->getId(),
            [
                'attack' => $unit->getStrength(),
                'defense' => $unit->getDefense(),
                'agility' => $unit->getAgility(),
            ],
            $unit->isWeakened(),
            $definition->getFoodConsumptionRate()
        );
    }

    public static function fromDefinition(UnitDefinition $unitDefinition): self
    {
        return new self(
            '',  // No ID for definition preview
            $unitDefinition->getName(),
            $unitDefinition->getDescription(),
            '',  // No building ID for definition preview
            [
                'attack' => $unitDefinition->getBaseAttack(),
                'defense' => $unitDefinition->getBaseDefense(),
                'agility' => $unitDefinition->getBaseAgility(),
            ],
            false,  // Not weakened by default
            $unitDefinition->getFoodConsumptionRate()
        );
    }
}