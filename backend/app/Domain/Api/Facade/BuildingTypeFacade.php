<?php declare(strict_types = 1);

namespace App\Domain\Api\Facade;

use App\Domain\Api\Response\BuildingTypeResDto;
use App\Domain\Building\BuildingType;
use App\Model\Database\EntityManager;
use App\Model\Exception\Runtime\Database\EntityNotFoundException;

class BuildingTypeFacade {

	public function __construct(private EntityManager $em) {}

	/**
	 * @return BuildingTypeResDto[]
	 */
	public function findAll(int $limit = 10, int $offset = 0) : array {
		return $this->findBy([], ['id' => 'ASC'], $limit, $offset);
	}

	/**
	 * @param mixed[]  $criteria
	 * @param string[] $orderBy
	 *
	 * @return BuildingTypeResDto[]
	 */
	public function findBy(
		array $criteria = [],
		array $orderBy = ['id' => 'ASC'],
		int   $limit = 10,
		int   $offset = 0,
	) : array {
		$entities = $this->em->getRepository(BuildingType::class)->findBy($criteria, $orderBy, $limit, $offset);
		$result = [];

		foreach ($entities as $entity) {
			$result[] = BuildingTypeResDto::from($entity);
		}

		return $result;
	}

	public function findOne(int $id) : BuildingTypeResDto {
		return $this->findOneBy(['id' => $id]);
	}

	/**
	 * @param array<string, mixed> $criteria
	 * @param array<string, mixed>|null $orderBy
	 */
	public function findOneBy(array $criteria, ?array $orderBy = NULL) : BuildingTypeResDto {
		$entity = $this->em
			->getRepository(BuildingType::class)
			->findOneBy($criteria, $orderBy);

		if ($entity === NULL) {
			throw new EntityNotFoundException();
		}

		return BuildingTypeResDto::from($entity);
	}

}