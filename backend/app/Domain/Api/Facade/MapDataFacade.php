<?php declare(strict_types = 1);

namespace App\Domain\Api\Facade;

use App\Domain\Player\Player;
use App\Domain\Region\RegionLevel;
use App\Model\Database\EntityManager;
use App\Model\Utils\GeoData;

class MapDataFacade {

	public function __construct(
		private readonly EntityManager 	$em,
	) {}

	public function getGeoJsonByPlayerBbox(Player $player, float $xMin, float $yMin, float $xMax, float $yMax) : string {
		$level = RegionLevel::lvl5;

		$longestSideLength = $this->getLongestSideLength($xMin, $yMin, $xMax, $yMax);
		if ($longestSideLength > 2000) {
			$level = RegionLevel::lvl2;
		} elseif ($longestSideLength > 500) {
			$level = RegionLevel::lvl3;
		} elseif ($longestSideLength > 40) {
			$level = RegionLevel::lvl4;
		}

		$showResources = $longestSideLength < 20 ? '1' : '0';

		$playerId = $player->getId();
		$showBuildings = $longestSideLength < 20 ? '1' : '0';
		$showItems = $longestSideLength < 5 ? '1' : '0';

		// Calculate bounding box dimensions in degrees
		$dx = abs($xMax - $xMin);
		$dy = abs($yMax - $yMin);
		$longestSideDegrees = max($dx, $dy);

		// Determine simplification tolerance based on the longest side of the bounding box in degrees.
		// Simplification occurs for larger bounding boxes (equivalent to being zoomed out).
		// Tolerance increases as the bounding box size increases.
		// Threshold and factor might need tuning based on desired simplification levels.
		$longestSideThreshold = 0.05; // Approx 5.5km at equator. Simplify if bbox longest side is larger.
		$simplifyTolerance = 0.0;
		if ($longestSideDegrees > $longestSideThreshold) {
			// Calculate tolerance - increases linearly above the threshold.
			// Factor determines how aggressively simplification increases.
			// Reduced from 0.05 to 0.01, then to 0.002 to make simplification even less visually jarring for large areas.
			$toleranceFactor = 0.002;
			$simplifyTolerance = ($longestSideDegrees - $longestSideThreshold) * $toleranceFactor;
		}

		$query = $this->em->getConnection()->executeQuery(
<<<SQL
WITH
	/* Regions of the given level */
	polygon_features AS (
		SELECT 'Feature' AS type,
			ST_AsGeoJSON(CASE WHEN CAST(? AS double precision) > 0 THEN ST_SimplifyPreserveTopology(lg.way, CAST(? AS double precision)) ELSE lg.way END)::json AS geometry,
			row_to_json((
				SELECT l FROM (
					SELECT 
						gr.id as id,
						'region' as type,
						gr.name, 
						(SELECT owner FROM (SELECT id, nickname FROM public.game_player WHERE id = gr.owner_id) AS owner)
				) AS l
			)) AS properties
		FROM public.game_region AS gr
		JOIN public.osm_polygon AS lg ON lg.osm_id = gr.osm_id
		WHERE 
			gr.level = ? AND
			lg.way && ST_MakeEnvelope(?, ?, ?, ?, 4326)
	), 
	
	/* Buildings */
	building_features AS (
		SELECT 'Feature' AS type,
			ST_AsGeoJSON(gb.position)::json AS geometry,
			row_to_json((
				SELECT l FROM (
					SELECT 
						gb.id as id,
						'building' as type,
						gbt.slug as building_type,
						gb.name as name,
						gb.state as state,
						gb.state_valid_to as state_valid_to,
						(SELECT owner FROM (SELECT id, nickname FROM public.game_player WHERE id = gb.player_id) AS owner)
				) AS l
			)) AS properties
		FROM public.game_building AS gb
		JOIN public.game_building_type AS gbt ON gb.building_type_id = gbt.id
		WHERE 
			gb.position && ST_MakeEnvelope(?, ?, ?, ?, 4326) AND
			(
				gb.player_id = ? OR
				'1' = ? /* Show buildings */
			)
	),
	
	/* Items */
	item_features AS (
		SELECT 'Feature' AS type,
			ST_AsGeoJSON(gip.position)::json AS geometry,
			row_to_json((
				SELECT l FROM (
					SELECT 
						gi.id as id,
						'item' as type,
						gid.name as name,
						gid.description as description,
						gid.commonness as commonness
				) AS l
			)) AS properties
		FROM public.game_item_placed AS gip
		JOIN public.game_item AS gi ON gi.id = gip.item_id
		JOIN public.game_item_definition AS gid ON gid.id = gi.item_definition_id
		WHERE 
			gip.position && ST_MakeEnvelope(?, ?, ?, ?, 4326) AND 
			'1' = ? /* Show items */
	),
	
	/* Resource regions */
	resources_features AS (
		SELECT 'Feature' AS type,
			ST_AsGeoJSON(CASE WHEN CAST(? AS double precision) > 0 THEN ST_SimplifyPreserveTopology(lg.way, CAST(? AS double precision)) ELSE lg.way END)::json AS geometry,
			row_to_json((
				SELECT l FROM (
					SELECT 
						gr.id as id,
						gr.name as name,
						'resource' as type,
						gr.resource_region_type AS resource,
						ST_AsGeoJSON(gr.position)::json AS pos
				) AS l
			)) AS properties
		FROM public.game_region AS gr
		JOIN public.osm_polygon AS lg ON lg.osm_id = gr.osm_id
		WHERE 
			gr.type = 'resource' AND
			gr.position && ST_MakeEnvelope(?, ?, ?, ?, 4326) AND
			'1' = ?
	)

SELECT row_to_json(fc)
FROM (
    SELECT 'FeatureCollection' AS type, array_to_json(array_agg(f)) AS features
    FROM (
        SELECT * FROM polygon_features
        UNION ALL
        SELECT * FROM building_features
        UNION ALL
        SELECT * FROM item_features
        UNION ALL
        SELECT * FROM resources_features
    ) AS f
) AS fc;
SQL
		, [
			// Parameters for polygon_features simplification
			$simplifyTolerance, $simplifyTolerance,
			// Original parameters for polygon_features WHERE clause
			$level->value,
			$xMin, $yMin, $xMax, $yMax,
			// Parameters for building_features WHERE clause
			$xMin, $yMin, $xMax, $yMax,
			$playerId, $showBuildings,
			// Parameters for item_features WHERE clause
			$xMin, $yMin, $xMax, $yMax,
			$showItems,
			// Parameters for resources_features simplification
			$simplifyTolerance, $simplifyTolerance,
			// Original parameters for resources_features WHERE clause
			$xMin, $yMin, $xMax, $yMax,
			$showResources,
		]);

		$result = $query->fetchAssociative();
		if ($result === false) {
			throw new \RuntimeException('Failed to fetch GeoJSON data');
		}

		return (string) $result['row_to_json'];
	}

	private function getLongestSideLength(
		float $minLon,
		float $minLat,
		float $maxLon,
		float $maxLat,
	) : float {
		// Calculate width (distance in east-west direction)
		$width = GeoData::distance($minLon, $minLat, $maxLon, $minLat);

		// Calculate height (distance in north-south direction)
		$height = GeoData::distance($minLon, $minLat, $minLon, $maxLat);

		// Return the longest side length in kilometers
		return max($width, $height) / 1000;
	}

}