<?php declare(strict_types = 1);

namespace App;

use App\Model\Utils\Caster;
use Contributte\Middlewares\Application\IApplication as ApiApplication;
use Doctrine\ORM\EntityManagerInterface;
use Nette\Application\Application as UIApplication;
use Nette\Bootstrap\Configurator;
use Nette\DI\Container;
use Symfony\Component\Console\Application as SymfonyApplication;
use Tracy\Debugger;

class Bootstrap {

	/**
	 * @param string[]|bool|string $debug
	 */
	public static function boot(array|bool|string $debug = FALSE) : Configurator {
		$configurator = new Configurator;
		$appDir = dirname(__DIR__);

		$configurator->setDebugMode($debug);

		$configurator->enableTracy($appDir . '/var/log');
		Debugger::$errorTemplate = $appDir . '/../resources/tracy/500.txt';

		$configurator->setTempDirectory($appDir . '/var/temp');

		$configurator->createRobotLoader()
					 ->addDirectory(__DIR__)
					 ->register();

		// Provide some parameters
		$configurator->addStaticParameters(
			[
				'rootDir' => realpath(__DIR__ . '/..'),
				'appDir'  => __DIR__,
				'wwwDir'  => realpath(__DIR__ . '/../www'),
			],
		);

		if ($configurator->isDebugMode()) {
			$configurator->addConfig($appDir . '/config/env/dev.neon');
		} else {
			$configurator->addConfig($appDir . '/config/env/prod.neon');
		}

		if (file_exists($appDir . '/config/local.neon')) {
			$configurator->addConfig($appDir . '/config/local.neon');
		}

		if (getenv('APP_DB_HOST') !== FALSE) {
			$configurator->addStaticParameters([
				'database' => [
					'driver' 		=> 'pdo_pgsql',
					'serverVersion' => getenv('APP_DB_VERSION') ?: '17.3',
					'host' 			=> getenv('APP_DB_HOST'),
					'port' 			=> getenv('APP_DB_PORT') ?: 5432,
					'dbname' 		=> getenv('APP_DB_NAME'),
					'user' 			=> getenv('APP_DB_USER'),
					'password' 		=> getenv('APP_DB_PASSWORD'),
				],
			]);
		}

		if (getenv('APP_OAUTH_KEY') !== FALSE) {
			$configurator->addStaticParameters([
				'oauth_server' => [
					'encryptionKey' 	=> getenv('APP_OAUTH_KEY'),
					'privateKeyPath' 	=> getenv('APP_OAUTH_KEYS_PATH') . '/private.key',
					'publicKeyPath' 	=> getenv('APP_OAUTH_KEYS_PATH') . '/public.key',
					'keyPassPhrase' 	=> getenv('APP_OAUTH_KEY_PASS_PHRASE'),
				],
			]);
		}

		if (getenv('APP_SENTRY_ENABLED') !== FALSE) {
			$configurator->addStaticParameters([
				'sentry' => [
					'enable' 	=> getenv('APP_SENTRY_ENABLED'),
					'dns' 		=> getenv('APP_SENTRY_DNS'),
				],
			]);
		}

		if (getenv('APP_GROQ_KEY') !== FALSE) {
			$configurator->addStaticParameters([
				'groq' => [
					'apiKey' => getenv('APP_GROQ_KEY'),
				],
			]);
		}

		if (getenv('APP_REVERSE_PROXY') !== FALSE) {
			$configurator->addConfig([
				'http' => [
					'proxy' => explode(',', getenv('APP_REVERSE_PROXY')),
				],
			]);
		}

		return $configurator;
	}

	public static function bootCli() : Container {
		return self::boot(in_array('--debug', $_SERVER['argv'] ?? [], true) || getenv('NETTE_DEBUG') || getenv('USER') === 'honza')
				   ->addStaticParameters(['scope' => 'cli'])
				   ->createContainer();
	}

	public static function runWeb() : void {
		self::boot('secret@127.0.0.1')
			->addStaticParameters(['scope' => 'web'])
			->createContainer()
			->getByType(UIApplication::class)
			->run();
	}

	public static function runCli() : void {
		self::boot(in_array('--debug', $_SERVER['argv'] ?? [], true) || getenv('NETTE_DEBUG') || getenv('USER') === 'honza')
			->addStaticParameters(['scope' => 'cli'])
			->createContainer()
			->getByType(SymfonyApplication::class)
			->run();
	}

	public static function runApi() : void {
		self::boot('secret@127.0.0.1')
			->addStaticParameters(['scope' => 'api'])
			->createContainer()
			->getByType(ApiApplication::class)
			->run();
	}

}
