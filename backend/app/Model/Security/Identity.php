<?php declare(strict_types = 1);

namespace App\Model\Security;

use Nette\Security\SimpleIdentity as NetteIdentity;

class Identity extends NetteIdentity {

	private array $userData;

	public function __construct($id, $roles = null, array $data = []) {
		parent::__construct($id, $roles, $data);
		$this->userData = $data;
	}

	public function getFullname() : string {
		return sprintf('%s %s', $this->userData['name'] ?? '', $this->userData['surname'] ?? '');
	}

}
