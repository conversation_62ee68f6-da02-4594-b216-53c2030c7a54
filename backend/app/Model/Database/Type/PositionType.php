<?php declare(strict_types = 1);

namespace App\Model\Database\Type;

use App\Model\Utils\Position;
use Doctrine\DBAL\Platforms\AbstractPlatform;
use Doctrine\DBAL\Types\ConversionException;
use Doctrine\DBAL\Types\JsonType;
use JsonException;

class PositionType extends JsonType {

	public const NAME = 'position';

	public function getName() : string {
		return self::NAME;
	}

	/**
	 * {@inheritDoc}
	 */
	public function convertToDatabaseValue($value, AbstractPlatform $platform) : string|null {
		if ($value === NULL) {
			return NULL;
		}

		if (!$value instanceof Position) {
			throw ConversionException::conversionFailedInvalidType($value, self::NAME, ['null', Position::class]);
		}

		$value = [
			'lat' => $value->getLat(),
			'lng' => $value->getLng(),
		];

		try {
			return json_encode($value, JSON_THROW_ON_ERROR | JSON_PRESERVE_ZERO_FRACTION);
		} catch (JsonException $e) {
			throw ConversionException::conversionFailedSerialization($value, 'json', $e->getMessage(), $e);
		}
	}

	/**
	 * {@inheritDoc}
	 */
	public function convertToPHPValue($value, AbstractPlatform $platform) : ?Position {
		if ($value === null || $value === '') {
			return null;
		}

		if (is_resource($value)) {
			$value = stream_get_contents($value);
		}

		try {
			return Position::fromArray(json_decode($value, true, 512, JSON_THROW_ON_ERROR | JSON_OBJECT_AS_ARRAY));
		} catch (JsonException $e) {
			throw ConversionException::conversionFailed($value, self::NAME, $e);
		}
	}

}